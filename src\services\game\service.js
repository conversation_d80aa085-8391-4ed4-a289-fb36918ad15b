const util = require("node:util");
const AssetUtil = require("@common/AssetUtil");
const ConfigManager = require("@common/ConfigManager");
const logger = require("@common/Logger");
const Redis = require("@common/Redis");
const RedisKeys = require("@common/RedisKeys");
const Responses = require("@common/Responses");
const LanguageKeys = require("@common-constants/LanguageKeys");
const GameTypes = require("@common-constants/GameTypes");
const Game = require("@common-models/Game");
const GameDetail = require("@common-models/GameDetail");
const GameLike = require("@common-models/GameLike");
const GamePartyConfig = require("@common-models/GamePartyConfig");
const GameUpdate = require("@common-models/GameUpdate");
const Page = require("@common-models/Page");
const User = require("@common-models/User");
const RankingTypes = require("@common-constants/RankingTypes");
const CategoryTypes = require("@common-constants/CategoryTypes");

async function getRecentlyPlayedList(userId, language) {
    const gamesData = [];
    return Responses.success(gamesData);
}

async function getRecentlyPlayedFriendList(userId, language) {
    logger.warn("GetRecentlyPlayedFriendList: Implementation needed");
    return Responses.innerError();
}

async function getGameDetails(userId, gameId, language) {
    const game = await Game.fromGameId(gameId, language);
    if (!game || game.isExcluded()) {
        return Responses.gameNotExists();
    }
    
    const gameDetail = await GameDetail.fromGameId(gameId, language);
    const gameLike = await GameLike.fromUserId(userId);

    return Responses.success({
        ...game.response(),
        ...gameLike.response(gameId),
        bannerPic: [gameDetail.getBannerUrl()],
        warmUpResponse: gameDetail.response()
    });
}

async function likeGame(userId, gameId) {
    const game = await Game.fromGameId(gameId, LanguageKeys.NO_LANGUAGE);
    if (!game || game.isExcluded()) {
        return Responses.gameNotExists();
    }

    const gameLike = await GameLike.fromUserId(userId);
    if (gameLike.getGames().includes(gameId)) {
        return Responses.gameAlreadyLiked();
    }

    gameLike.addGame(gameId);

    await gameLike.save();
    await GameLike.addLike(gameId);

    return Responses.success();
}

async function gamePartyAuth(userId) {
    logger.warn("GamePartyAuth: Implementation needed");
    return Responses.innerError();
}

async function getResourceInfo(gameId, engineVersion, resVersion) {
    logger.warn("GetResourceInfo: Implementation needed");
    return Responses.innerError();
}

async function getGameInformation(gameId, language) {
    logger.warn("GetGameInformation: Implementation needed");
    const game = await Game.fromGameId(gameId);
    if (!game) {
        return Responses.gameNotExists();
    }

    // TODO: Language support

    return Responses.success();
}

async function getChatRoom(userId, roomName) {
    logger.warn("GetChatRoom: Implementation needed");
    return Responses.success({roomId: 23, roomName: roomName});
}

async function deleteChatRoom(roomId) {
    logger.warn("DeleteChatRoom: Implementation needed");
    return Responses.innerError();
}

async function getGamesRecommendationByType(language, orderType, pageNo, pageSize) {
    let key;
    
    switch (orderType) {
        case GameTypes.RELEASE_TIME:
            key = RedisKeys.GAME_RELEASE_TIME;
            break;
        case GameTypes.MAXIMUM_LIKES:
            key = RedisKeys.GAME_LIKE_COUNT;
            break;
        case GameTypes.MAXIMUM_PLAYERS:
            key = RedisKeys.GAME_PLAYER_COUNT;
            break;
        default:
            return Responses.invalidType();
    }

    const data = await Redis.getScoreList(key, pageNo, pageSize, true);
    const totalSize = await Redis.getSetSize(key);
    
    for (let i = 0; i < data.length; i++) {
        const gameId = data[i].value;
        data[i] = await Game.fromGameId(gameId, language);
    }

    const games = new Page(data.filter(x => x.excludeGame == 0).map(x => x.response()), totalSize, pageNo, pageSize);
    return Responses.success(games);
}

async function getGames(language, gameType, orderType, pageNo, pageSize) {
    let key;
    let type;
    
    switch (orderType) {
        case GameTypes.RELEASE_TIME:
            key = RedisKeys.GAME_RELEASE_TIME;
            break;
        case GameTypes.MAXIMUM_LIKES:
            key = RedisKeys.GAME_LIKE_COUNT;
            break;
        case GameTypes.MAXIMUM_PLAYERS:
            key = RedisKeys.GAME_PLAYER_COUNT;
            break;
        default:
            return Responses.invalidType();
    }

    switch (gameType) {
        case CategoryTypes.ALL:
            // do nothing
            break;
        case CategoryTypes.PVP:
            type = GameTypes.PVP;
            break;
        case CategoryTypes.SIM:
            type = GameTypes.SIM;
            break;
        case CategoryTypes.AVG:
            type = GameTypes.AVG;
            break;
        case CategoryTypes.STG:
            type = GameTypes.STG;
            break
        default:
            return Responses.invalidType();
    }

    const data = await Redis.getScoreList(key, pageNo, pageSize, true);
    const totalSize = await Redis.getSetSize(key);
    
    for (let i = 0; i < data.length; i++) {
        const gameId = data[i].value;
        data[i] = await Game.fromGameId(gameId, language);
    }

    if (!type) {
        const games = new Page(data.filter(x => x.excludeGame == 0).map(x => x.response()), totalSize, pageNo, pageSize);
        return Responses.success(games);
    }
    
    const games = new Page(data.filter(x => x.excludeGame == 0 && x.gameTypes.includes(type)).map(x => x.response()), totalSize, pageNo, pageSize);
    return Responses.success(games);
}

async function getGameRank(gameId, type, pageNo, pageSize) {
    let key = null;

    switch (type) {
        case RankingTypes.WEEK:
            key = util.format(RedisKeys.CACHE_GAME_WEEK_RANK, gameId);
            break;
        case RankingTypes.MONTH:
            key = util.format(RedisKeys.CACHE_GAME_MONTH_RANK, gameId);
            break;
        case RankingTypes.ALL:
            key = util.format(RedisKeys.GAME_OVERALL_RANK, gameId);
            break;
    }

    const data = await Redis.getScoreList(key, pageNo, pageSize, true);
    const totalSize = await Redis.getSetSize(key);
    
    let expireTime = 0;
    if (type != RankingTypes.ALL) {
        expireTime = await Redis.getExpire(key);
    }

    const startRank = (pageNo - 1) * pageSize;

    const rankedData = await Promise.all(data.map(async (item, index) => {
        const userId = item.value;
        const user = await User.fromUserId(userId);
        return {
            userId: user.getUserId(),
            nickName: user.getNickname(),
            headPic: user.getProfilePic(),
            integral: Math.floor(item.score),
            rank: startRank + index + 1
        };
    }));

    return Responses.success({
        remainTime: expireTime * 1000,
        pageInfo: new Page(rankedData, totalSize, pageNo, pageSize) 
    });
}

async function getUserGameRank(userId, gameId, type) {
    const user = await User.fromUserId(userId);

    let key;

    switch (type) {
        case RankingTypes.WEEK:
            key = util.format(RedisKeys.CACHE_GAME_WEEK_RANK, gameId);
            break;
        case RankingTypes.MONTH:
            key = util.format(RedisKeys.CACHE_GAME_MONTH_RANK, gameId);
            break;
        case RankingTypes.ALL:
            key = util.format(RedisKeys.GAME_OVERALL_RANK, gameId);
            break;
        default:
            return Responses.invalidType();
    }

    let score = await Redis.getKeyScore(key, user.getUserId());
    let rank = await Redis.getKeyRank(key, user.getUserId(), true);

    return Responses.success({
        userId: user.getUserId(),
        nickName: user.getNickname(),
        headPic: user.getProfilePic(),
        integral: score,
        rank: rank + 1 // Zero-based
    });
}

async function getPartyGames(userId, language) {
    const partyGames = await Game.listPartyGames(language);
    return Responses.success(partyGames);
}

async function getPartyGameConfig(gameId) {
    const partyConfig = await GamePartyConfig.fromGameId(gameId);
    if (!partyConfig) {
        return Responses.gameNotExists();
    }

    return Responses.success(partyConfig.response());
}

async function getGameTeamMembers(teamId) {
    logger.warn("GetGameTeamMembers: Implementation needed");
    return Responses.innerError();
}

async function getGameTurntableInfo(gameId) {
    logger.warn("GetGameTurntableInfo: Implementation needed");
    return Responses.innerError();
}

async function getGameTurntableReward(userId, gameId) {
    logger.warn("GetGameTurntableReward: Implementation needed");
    return Responses.innerError();
}

async function getGameTurntableRewardName(userId, gameId) {
    logger.warn("GetGameTurntableRewardName: Implementation needed");
    return Responses.success();
}

async function getEngineInfo(gameId, engineVersion) {
    const game = await Game.fromGameId(gameId, LanguageKeys.NO_LANGUAGE);
    if (!game || game.isExcluded()) {
        return Responses.gameNotExists();
    }

    const resConfig = await ConfigManager.get(game.getGameId());
    if (!resConfig) {
        return Responses.success({});
    }

    return Responses.success({
        downloadUrl: AssetUtil.getGameResUrl(game.getGameId()),
        ...resConfig
    });
}

async function getGameUpdateInfo(gameId, language, engineVersion) {
    const gameUpdate = await GameUpdate.fromGameId(gameId);
    return Responses.success(gameUpdate.response());
}

async function getGameListUpdateInfo(engineVersion, language) {
    logger.warn("GetGameListUpdateInfo: Implementation needed");
    return Responses.innerError();
}

async function getCommunityGames(pageNo, pageSize) {
    logger.warn("GetCommunityGames: Implementation needed");
    return Responses.success(Page.empty());
}

async function getCommunityGamesStatus() {
    logger.warn("GetCommunityGamesStatus: Implementation needed");
    return Responses.innerError();
}

module.exports = {
    getRecentlyPlayedList: getRecentlyPlayedList,
    getRecentlyPlayedFriendList: getRecentlyPlayedFriendList,
    likeGame: likeGame,
    gamePartyAuth: gamePartyAuth,
    getResourceInfo: getResourceInfo,
    getGameDetails: getGameDetails,
    getGameInformation: getGameInformation,
    getChatRoom: getChatRoom,
    deleteChatRoom: deleteChatRoom,
    getGamesRecommendationByType: getGamesRecommendationByType,
    getGames: getGames,
    getGameRank: getGameRank,
    getUserGameRank: getUserGameRank,
    getPartyGames: getPartyGames,
    getPartyGameConfig: getPartyGameConfig,
    getGameTeamMembers: getGameTeamMembers,
    getGameTurntableInfo: getGameTurntableInfo,
    getGameTurntableReward: getGameTurntableReward,
    getGameTurntableRewardName: getGameTurntableRewardName,
    getEngineInfo: getEngineInfo,
    getGameUpdateInfo: getGameUpdateInfo,
    getGameListUpdateInfo: getGameListUpdateInfo,
    getNoticeInfo: getNoticeInfo,
    getCommunityGames: getCommunityGames,
    getCommunityGamesStatus: getCommunityGamesStatus
}